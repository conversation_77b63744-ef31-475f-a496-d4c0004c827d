import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";

export default async (request: NextRequest) => {
  const { pathname } = request.nextUrl;

  // Define protected routes
  const protectedRoutes = ["/dashboard"];
  
  // Define auth routes (redirect to dashboard if already authenticated)
  const authRoutes = ["/auth"];

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route => 
    pathname.startsWith(route)
  );

  // Check if the current path is an auth route
  const isAuthRoute = authRoutes.some(route => 
    pathname.startsWith(route)
  );

  try {
    // Get session from the request
    const session = await auth.api.getSession({
      headers: request.headers,
    });

    // If trying to access protected route without session, redirect to auth
    if (isProtectedRoute && !session) {
      const authUrl = new URL("/auth", request.url);
      return NextResponse.redirect(authUrl);
    }

    // If trying to access auth route with valid session, redirect to dashboard
    if (isAuthRoute && session) {
      const dashboardUrl = new URL("/dashboard", request.url);
      return NextResponse.redirect(dashboardUrl);
    }

    // Allow the request to continue
    return NextResponse.next();
  } catch (error) {
    // If there's an error getting the session and it's a protected route, redirect to auth
    if (isProtectedRoute) {
      const authUrl = new URL("/auth", request.url);
      return NextResponse.redirect(authUrl);
    }

    // For non-protected routes, continue normally
    return NextResponse.next();
  }
};

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
