"use client";

import { useSession, signOut } from "@/lib/auth-client";
import { useRouter } from "next/navigation";

export default () => {
  const { data: session, isLoading } = useSession();
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div>Loading...</div>
      </div>
    );
  }

  if (!session) {
    router.push("/auth");
    return null;
  }

  const handleSignOut = async () => {
    await signOut();
    router.push("/auth");
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Dashboard</h1>

          <div className="mb-6">
            {session.user.image && (
              <img
                src={session.user.image}
                alt="Profile"
                className="w-16 h-16 rounded-full mx-auto mb-4"
              />
            )}
            <h2 className="text-lg font-medium text-gray-900">
              Welcome, {session.user.name || session.user.email}!
            </h2>
            <p className="text-gray-600">{session.user.email}</p>
          </div>

          <button
            onClick={handleSignOut}
            className="w-full bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-200"
          >
            Sign Out
          </button>
        </div>
      </div>
    </div>
  );
};
