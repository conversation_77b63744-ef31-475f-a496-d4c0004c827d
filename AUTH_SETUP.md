# Authentication Setup

This project uses BetterAuth with Google and GitHub OAuth providers, along with Redis for session storage.

## Prerequisites

1. **Docker & Docker Compose** - for running PostgreSQL, Redis, and MinIO
2. **Google OAuth App** - for Google authentication
3. **GitHub OAuth App** - for GitHub authentication

## Setup Instructions

### 1. Start the Services

```bash
docker-compose up -d
```

This will start:
- PostgreSQL on port 5432
- Redis on port 6379
- MinIO on ports 9000 (API) and 9001 (Console)

### 2. Configure OAuth Providers

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set authorized redirect URIs to: `http://localhost:3000/api/auth/callback/google`
6. Copy Client ID and Client Secret

#### GitHub OAuth Setup
1. Go to GitHub Settings → Developer settings → OAuth Apps
2. Click "New OAuth App"
3. Set Authorization callback URL to: `http://localhost:3000/api/auth/callback/github`
4. Copy Client ID and Client Secret

### 3. Update Environment Variables

Update the `.env` file with your OAuth credentials:

```env
GOOGLE_CLIENT_ID=your_actual_google_client_id
GOOGLE_CLIENT_SECRET=your_actual_google_client_secret

GITHUB_CLIENT_ID=your_actual_github_client_id
GITHUB_CLIENT_SECRET=your_actual_github_client_secret
```

### 4. Run Database Migrations

```bash
npm run db:push
# or
npx drizzle-kit push
```

### 5. Start the Development Server

```bash
npm run dev
```

## Usage

1. Visit `http://localhost:3000`
2. Click "Sign In / Sign Up" to go to the auth page
3. You can:
   - Register/login with email and password
   - Sign in with Google
   - Sign in with GitHub
4. After authentication, you'll be redirected to the dashboard
5. The dashboard shows your user information and a logout button

## Features

- ✅ Email/Password authentication
- ✅ Google OAuth
- ✅ GitHub OAuth
- ✅ Redis session storage
- ✅ PostgreSQL user storage
- ✅ Automatic redirects
- ✅ Session management
- ✅ Responsive design (basic styling)

## File Structure

```
src/
├── app/
│   ├── api/auth/[...all]/route.ts  # BetterAuth API handler
│   ├── auth/page.tsx               # Authentication page
│   ├── dashboard/page.tsx          # Protected dashboard
│   └── page.tsx                    # Home page
├── lib/
│   ├── auth.ts                     # Server-side auth config
│   └── auth-client.ts              # Client-side auth utilities
└── db/
    └── schema.ts                   # Database schema
```

## Troubleshooting

1. **Redis connection issues**: Make sure Redis is running via Docker
2. **OAuth errors**: Check that redirect URIs match exactly
3. **Database errors**: Ensure PostgreSQL is running and migrations are applied
4. **Environment variables**: Make sure all required env vars are set
